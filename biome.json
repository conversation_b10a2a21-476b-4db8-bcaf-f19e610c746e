{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true, "includes": ["**", "!node_modules", "!.next", "!dist", "!build"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "lineEnding": "lf"}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noUnknownAtRules": "off"}}, "domains": {"next": "recommended", "react": "recommended"}}, "assist": {"actions": {"source": {"organizeImports": "on"}}}}