{"name": "auto-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@clerk/nextjs": "^6.33.0", "@google/generative-ai": "^0.24.1", "@mdxeditor/editor": "^3.47.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.27.3", "framer-motion": "^12.23.22", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "next": "15.5.4", "next-themes": "^0.4.6", "octokit": "^5.0.3", "openai": "^6.2.0", "react": "19.1.0", "react-dom": "19.1.0", "server-only": "^0.0.1", "sonner": "^2.0.7", "svix": "^1.76.1", "tailwind-merge": "^3.3.1", "zod": "^4.1.11"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@clerk/types": "^4.89.0", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.4.0", "typescript": "^5"}}