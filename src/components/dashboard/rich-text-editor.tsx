"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import type { MDXEditorMethods } from "@mdxeditor/editor";
import {
  BlockTypeSelect,
  BoldItalicUnderlineToggles,
  codeBlockPlugin,
  codeMirrorPlugin,
  CreateLink,
  headingsPlugin,
  InsertCodeBlock,
  linkDialogPlugin,
  linkPlugin,
  listsPlugin,
  ListsToggle,
  markdownShortcutPlugin,
  MDXEditor,
  quotePlugin,
  thematicBreakPlugin,
  toolbarPlugin,
  UndoRedo,
} from "@mdxeditor/editor";
import "@mdxeditor/editor/style.css";
import { useRef } from "react";

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

export function RichTextEditor({ content, onChange, onSave, onCancel }: RichTextEditorProps) {
  const editorRef = useRef<MDXEditorMethods>(null);

  return (
    <div className="border border-border rounded-lg overflow-hidden">
      {/* Editor */}
      <div className="[&_.mdxeditor]:min-h-[300px] [&_.mdxeditor-toolbar]:bg-card [&_.mdxeditor-toolbar]:text-card-foreground [&_.mdxeditor-toolbar]:border-b [&_.mdxeditor-toolbar]:border-border [&_.mdxeditor-root-contenteditable]:bg-background [&_.mdxeditor-root-contenteditable]:text-foreground [&_.mdxeditor-root-contenteditable]:p-4 [&_.mdxeditor-root-contenteditable]:prose [&_.mdxeditor-root-contenteditable]:prose-sm [&_.mdxeditor-root-contenteditable]:max-w-none [&_.mdxeditor-root-contenteditable]:prose-headings:text-foreground [&_.mdxeditor-root-contenteditable]:prose-strong:text-foreground [&_.mdxeditor-root-contenteditable]:prose-code:bg-muted [&_.mdxeditor-root-contenteditable]:prose-code:text-foreground [&_.cm-editor]:bg-muted [&_.cm-editor]:text-foreground dark:[&_.cm-editor]:bg-muted/60 [&_.cm-gutters]:bg-muted [&_.cm-gutters]:text-muted-foreground dark:[&_.cm-gutters]:bg-muted/60 dark:[&_.mdxeditor-toolbar]:bg-card/80 dark:[&_.mdxeditor-toolbar]:text-card-foreground">
        <MDXEditor
          ref={editorRef}
          markdown={content}
          onChange={onChange}
          plugins={[
            headingsPlugin(),
            listsPlugin(),
            quotePlugin(),
            thematicBreakPlugin(),
            linkPlugin(),
            linkDialogPlugin(),
            codeBlockPlugin({ defaultCodeBlockLanguage: "js" }),
            codeMirrorPlugin({
              codeBlockLanguages: {
                js: "JavaScript",
                ts: "TypeScript",
                tsx: "TypeScript JSX",
                jsx: "JavaScript JSX",
                python: "Python",
                bash: "Bash",
                json: "JSON",
              },
            }),
            markdownShortcutPlugin(),
            toolbarPlugin({
              toolbarContents: () => (
                <>
                  <UndoRedo />
                  <BoldItalicUnderlineToggles />
                  <BlockTypeSelect />
                  <ListsToggle />
                  <CreateLink />
                  <InsertCodeBlock />
                </>
              ),
            }),
          ]}
        />
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end gap-2 p-3 border-t border-border bg-card text-card-foreground">
        <Button type="button" variant="outline" size="sm" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="button" size="sm" onClick={onSave}>
          Save
        </Button>
      </div>

      {/* Selection Colors */}
      <style jsx>{`
        .mdxeditor-root-contenteditable ::selection,
        .cm-content ::selection {
          background: hsl(var(--primary) / 0.25);
          color: hsl(var(--foreground));
        }
      `}</style>
    </div>
  );
}
